
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
import numpy as np
from matplotlib.ticker import FuncFormatter
import matplotlib.dates as mdates
from .tod_config import get_slot_order, get_slot_color_map, normalize_slot_name, add_slot_labels_with_time, SLOT_METADATA
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

from helper.setup_logger import setup_logger

logging = setup_logger("tod_tab_visual", "tod_tab_visual.log")

def format_thousands(x, pos):
    """Format numbers for display with K suffix for thousands"""
    try:
        if x >= 1000:
            return f'{x/1000:.0f}K'
        else:
            return f'{x:.0f}'
    except (TypeError, ValueError) as e:
        logging.warning(f"Error formatting number {x}: {str(e)}")
        return str(x) if x is not None else '0'
    except Exception as e:
        logging.error(f"Unexpected error in format_thousands: {str(e)}")
        return '0'


##Monthly ToD Before Banking

def create_monthly_before_banking_plot(df: pd.DataFrame, plant_name: str):
    """
    Create monthly ToD-wise generation vs. consumption stacked bar chart.
    """
    try:
        logging.info(f"Creating monthly before banking plot for {plant_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for monthly before banking plot - {plant_name}")
            fig, ax = plt.subplots(figsize=(14, 8))
            ax.text(0.5, 0.5, "No data available for the selected period", ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Monthly ToD Analysis - {plant_name}", fontsize=16, fontweight='bold')
            return fig
        
        # Validate required columns
        required_columns = ['date', 'slot', 'generation_kwh', 'consumption_kwh']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in data: {missing_columns}")
            fig, ax = plt.subplots(figsize=(14, 8))
            ax.text(0.5, 0.5, "Data structure error - please contact support", ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Monthly ToD Analysis - {plant_name}", fontsize=16, fontweight='bold')
            return fig
        
        fig, ax = plt.subplots(figsize=(14, 8))
        
    except Exception as e:
        logging.error(f"Error initializing monthly before banking plot for {plant_name}: {str(e)}")
        fig, ax = plt.subplots(figsize=(14, 8))
        ax.text(0.5, 0.5, "Error loading chart - please try again", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly ToD Analysis - {plant_name}", fontsize=16, fontweight='bold')
        return fig

    # Step 1: Extract month
    df['date'] = pd.to_datetime(df['date'])
    df['month'] = df['date'].dt.to_period('M').astype(str)

    # Step 2: Normalize slots
    df['slot'] = df['slot'].apply(normalize_slot_name)

    # Step 3: Pivot tables
    gen_pivot = df.pivot_table(index='month', columns='slot', values='generation_kwh', aggfunc='sum', fill_value=0)
    cons_pivot = df.pivot_table(index='month', columns='slot', values='consumption_kwh', aggfunc='sum', fill_value=0)

    # Step 4: Order and align columns
    slot_order = list(reversed(get_slot_order()))  # Top-down visual stacking
    slot_colors = get_slot_color_map()

    for slot in slot_order:
        if slot not in gen_pivot.columns:
            gen_pivot[slot] = 0.0
        if slot not in cons_pivot.columns:
            cons_pivot[slot] = 0.0
    gen_pivot = gen_pivot[slot_order].astype(float)
    cons_pivot = cons_pivot[slot_order].astype(float)

    # Step 5: Plot
    x = np.arange(len(gen_pivot.index))
    bar_width = 0.4
    months = gen_pivot.index.tolist()

    gen_bottom = np.zeros(len(x))
    cons_bottom = np.zeros(len(x))

    legend_items = []

    for slot in slot_order:
        gen_bar = ax.bar(
            x - bar_width / 2,
            gen_pivot[slot].values,
            bar_width,
            bottom=gen_bottom,
            color=slot_colors.get(slot, '#aaa'),
            edgecolor='white',
            label=f'Gen {slot}'
        )
        gen_bottom += gen_pivot[slot].values
        legend_items.append((f'Gen {slot}', gen_bar[0]))

        cons_bar = ax.bar(
            x + bar_width / 2,
            cons_pivot[slot].values,
            bar_width,
            bottom=cons_bottom,
            color=slot_colors.get(slot, '#aaa'),
            edgecolor='black',
            hatch='///',
            label=f'Cons {slot}'
        )
        cons_bottom += cons_pivot[slot].values
        legend_items.append((f'Cons {slot}', cons_bar[0]))

    # Add total labels
    for i in range(len(x)):
        g_total = gen_bottom[i]
        c_total = cons_bottom[i]
        if g_total > 0:
            ax.text(x[i] - bar_width/2, g_total + g_total * 0.02, f"{g_total:.0f}", ha='center', va='bottom', fontsize=9, color='green')
        if c_total > 0:
            ax.text(x[i] + bar_width/2, c_total + c_total * 0.02, f"{c_total:.0f}", ha='center', va='bottom', fontsize=9, color='darkred')

    # Final touches
    ax.set_xticks(x)
    ax.set_xticklabels(months, rotation=45, ha='right')
    ax.set_ylabel("Energy (kWh)", fontsize=12)
    ax.set_xlabel("Month", fontsize=12)
    ax.yaxis.set_major_formatter(FuncFormatter(format_thousands))
    ax.grid(True, axis='y', linestyle='--', alpha=0.5)

    # Custom legend ordering: all Gen first, then Cons, both in stack order
    gen_legend = [item for item in legend_items if item[0].startswith("Gen")][::-1]
    cons_legend = [item for item in legend_items if item[0].startswith("Cons")][::-1]

    # Remove duplicates (if any), preserve order
    def deduplicate(items):
        seen = set()
        return [(l, h) for l, h in items if not (l in seen or seen.add(l))]

    ordered_legend_items = deduplicate(gen_legend) + deduplicate(cons_legend)

    ax.legend(
        [h for _, h in ordered_legend_items],
        [l for l, _ in ordered_legend_items],
        loc='upper left',
        bbox_to_anchor=(1.01, 1),
        frameon=False
    )

    try:
        plt.tight_layout()
        logging.info(f"Successfully created monthly before banking plot for {plant_name}")
        return fig
    except Exception as e:
        logging.error(f"Error finalizing monthly before banking plot for {plant_name}: {str(e)}")
        # Return the figure even if tight_layout fails
        return fig


def create_monthly_before_banking_plot_interactive(df: pd.DataFrame, plant_name: str):
    """
    Interactive Plotly version of monthly ToD-wise generation vs. consumption stacked bar chart with hover functionality.
    """
    try:
        logging.info(f"Creating interactive monthly before banking plot for {plant_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for interactive monthly before banking plot - {plant_name}")
            # Create empty figure with message
            fig = go.Figure()
            fig.add_annotation(
                text="No data available for the selected period",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Monthly ToD Analysis - {plant_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
        
        # Validate required columns
        required_columns = ['date', 'slot', 'generation_kwh', 'consumption_kwh']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in interactive data: {missing_columns}")
            fig = go.Figure()
            fig.add_annotation(
                text="Data structure error - please contact support",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Monthly ToD Analysis - {plant_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
        
    except Exception as e:
        logging.error(f"Error initializing interactive monthly before banking plot for {plant_name}: {str(e)}")
        fig = go.Figure()
        fig.add_annotation(
            text="Error loading chart - please try again",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(
            title=f"Monthly ToD Analysis - {plant_name}",
            xaxis=dict(showgrid=False, showticklabels=False),
            yaxis=dict(showgrid=False, showticklabels=False)
        )
        return fig

    try:
        # Step 1: Extract month
        df['date'] = pd.to_datetime(df['date'])
        df['month'] = df['date'].dt.to_period('M').astype(str)

        # Step 2: Normalize slots
        df['slot'] = df['slot'].apply(normalize_slot_name)

        # Step 3: Pivot tables
        gen_pivot = df.pivot_table(index='month', columns='slot', values='generation_kwh', aggfunc='sum', fill_value=0)
        cons_pivot = df.pivot_table(index='month', columns='slot', values='consumption_kwh', aggfunc='sum', fill_value=0)

        # Step 4: Order and align columns
        slot_order = list(reversed(get_slot_order()))  # Top-down visual stacking
        slot_colors = get_slot_color_map()
        
        logging.info(f"Successfully processed {len(df)} records for interactive plot - {plant_name}")
        
    except pd.errors.OutOfBoundsDatetime as e:
        logging.error(f"Date parsing error for interactive plot {plant_name}: {str(e)}")
        fig = go.Figure()
        fig.add_annotation(
            text="Date format error - please check your date range",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(title=f"Monthly ToD Analysis - {plant_name}")
        return fig
    except Exception as e:
        logging.error(f"Data processing error for interactive plot {plant_name}: {str(e)}")
        fig = go.Figure()
        fig.add_annotation(
            text="Data processing error - please try again",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(title=f"Monthly ToD Analysis - {plant_name}")
        return fig

    for slot in slot_order:
        if slot not in gen_pivot.columns:
            gen_pivot[slot] = 0.0
        if slot not in cons_pivot.columns:
            cons_pivot[slot] = 0.0
    gen_pivot = gen_pivot[slot_order].astype(float)
    cons_pivot = cons_pivot[slot_order].astype(float)
    
    months = gen_pivot.index.tolist()
    
    # Create figure
    fig = go.Figure()
    
    # Prepare data for hover information
    gen_totals = gen_pivot.sum(axis=1)
    cons_totals = cons_pivot.sum(axis=1)
    
    # Create x-axis positions for side-by-side bars
    x_positions = np.arange(len(months))
    bar_width = 0.35
    
    # Get slot time information
    slot_time_map = {slot: SLOT_METADATA[slot]["time"] for slot in SLOT_METADATA}
    
    # Keep track of processed slots for base calculation
    processed_gen_slots = []
    
    for slot in (slot_order):
        gen_values = gen_pivot[slot].values
        cons_values = cons_pivot[slot].values  # Get corresponding consumption values
        
        # Calculate base as sum of all previously processed slots (those below in the stack)
        gen_base = gen_pivot[processed_gen_slots].sum(axis=1) if processed_gen_slots else 0
        
        # Custom hover data for generation
        customdata_gen = []
        for i, month in enumerate(months):
            gen_val = gen_values[i]
            cons_val = cons_values[i]
            gen_total = gen_totals.iloc[i]
            cons_total = cons_totals.iloc[i]
            
            # Calculate additional metrics
            gen_percentage = (gen_val / gen_total * 100) if gen_total > 0 else 0
            gen_mwh = gen_val / 1000
            slot_difference = gen_val - cons_val
            slot_time = slot_time_map.get(slot, "N/A")
            
            customdata_gen.append([
                month,
                slot,
                gen_val,
                gen_total,
                'Generation',
                f"{gen_percentage:.1f}%",
                gen_mwh,
                cons_val,
                slot_difference,
                slot_time,
                cons_total
            ])
        
        fig.add_trace(
            go.Bar(
                x=x_positions - bar_width/2,
                y=gen_values,
                name=f'Gen {slot}',
                marker=dict(color=slot_colors.get(slot, '#aaa'), line=dict(color='white', width=1)),
                offsetgroup='generation',
                base=gen_base,
                customdata=customdata_gen,
                hovertemplate='<b>📅 Month:</b> %{customdata[0]}<br>' +
                             '<b>🕐 Slot:</b> %{customdata[1]} (%{customdata[9]})<br>' +
                             '<b>⚡ Type:</b> %{customdata[4]}<br>' +
                             '<b>💡 Value:</b> %{customdata[2]:,.0f} kWh (%{customdata[6]:.1f} MWh)<br>' +
                             '<b>📊 Slot Share:</b> %{customdata[5]} of monthly generation<br>' +
                             '<b>📈 Total Gen:</b> %{customdata[3]:,.0f} kWh<br>' +
                             '<b>🔄 Consumption (same slot):</b> %{customdata[7]:,.0f} kWh<br>' +
                             '<b>⚖️ Slot Difference:</b> %{customdata[8]:+,.0f} kWh<br>' +
                             '<extra></extra>',
                legendgroup='generation',
                legendgrouptitle_text="Generation"
            )
        )
        
        # Add this slot to processed list for next iteration's base calculation
        processed_gen_slots.append(slot)
    
    # Keep track of processed slots for base calculation
    processed_cons_slots = []
    
    for slot in (slot_order):
        cons_values = cons_pivot[slot].values
        gen_values = gen_pivot[slot].values  # Get corresponding generation values
        
        # Calculate base as sum of all previously processed slots (those below in the stack)
        cons_base = cons_pivot[processed_cons_slots].sum(axis=1) if processed_cons_slots else 0
        
        # Custom hover data for consumption
        customdata_cons = []
        for i, month in enumerate(months):
            cons_val = cons_values[i]
            gen_val = gen_values[i]
            cons_total = cons_totals.iloc[i]
            gen_total = gen_totals.iloc[i]
            
            # Calculate additional metrics
            cons_percentage = (cons_val / cons_total * 100) if cons_total > 0 else 0
            cons_mwh = cons_val / 1000
            slot_difference = gen_val - cons_val
            slot_time = slot_time_map.get(slot, "N/A")
            
            # Calculate slot-level efficiency metrics
            slot_coverage = (gen_val / cons_val * 100) if cons_val > 0 else 0
            
            customdata_cons.append([
                month,
                slot,
                cons_val,
                cons_total,
                'Consumption',
                f"{cons_percentage:.1f}%",
                cons_mwh,
                gen_val,
                slot_difference,
                slot_time,
                gen_total,
                slot_coverage
            ])
        
        fig.add_trace(
            go.Bar(
                x=x_positions + bar_width/2,
                y=cons_values,
                name=f'Cons {slot}',
                marker=dict(
                    color=slot_colors.get(slot, '#aaa'),
                    line=dict(color='black', width=1),
                    # pattern=dict(shape="/", bgcolor="rgba(255,255,255,0.5)", fgcolor=slot_colors.get(slot, '#aaa'))
                    pattern=dict(
                    shape="\\",  # diagonal opposite for variation
                    fgcolor="black",
                    size=5,
                    solidity=0.3,
                    bgcolor=slot_colors.get(slot, '#aaa')  # use slot color as background instead
                )
                ),
                offsetgroup='consumption',
                base=cons_base,
                customdata=customdata_cons,
                hovertemplate='<b>📅 Month:</b> %{customdata[0]}<br>' +
                             '<b>🕐 Slot:</b> %{customdata[1]} (%{customdata[9]})<br>' +
                             '<b>⚡ Type:</b> %{customdata[4]}<br>' +
                             '<b>🔌 Value:</b> %{customdata[2]:,.0f} kWh (%{customdata[6]:.1f} MWh)<br>' +
                             '<b>📊 Slot Share:</b> %{customdata[5]} of monthly consumption<br>' +
                             '<b>📈 Total Cons:</b> %{customdata[3]:,.0f} kWh<br>' +
                             '<b>🔄 Generation (same slot):</b> %{customdata[7]:,.0f} kWh<br>' +
                             '<b>⚖️ Slot Difference:</b> %{customdata[8]:+,.0f} kWh<br>' +
                             '<b>🎯 Slot Coverage:</b> %{customdata[11]:.1f}% (Gen/Cons ratio)<br>' +
                             '<extra></extra>',
                legendgroup='consumption',
                legendgrouptitle_text="Consumption"
            )
        )
        
        # Add this slot to processed list for next iteration's base calculation
        processed_cons_slots.append(slot)
    
    # Update layout
    fig.update_layout(
        xaxis=dict(
            title="Month",
            tickmode='array',
            tickvals=x_positions,
            ticktext=months,
            tickangle=45
        ),
        yaxis=dict(
            title="Energy (kWh)",
            tickformat=',.0f'
        ),
        barmode='group',
        height=600,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.01,
            groupclick="toggleitem",
            tracegroupgap=20
        ),
        hovermode='closest'
    )
    
    # Add grid
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    
    # Add annotations for totals
    for i, month in enumerate(months):
        gen_total = gen_totals.iloc[i]
        cons_total = cons_totals.iloc[i]
        
        if gen_total > 0:
            fig.add_annotation(
                x=x_positions[i] - bar_width/2,
                y=gen_total * 1.02,
                text=f"{gen_total:.0f}",
                showarrow=False,
                font=dict(size=10, color='green'),
                bgcolor="rgba(255,255,255,0.8)",
                bordercolor="green",
                borderwidth=1
            )
        
        if cons_total > 0:
            fig.add_annotation(
                x=x_positions[i] + bar_width/2,
                y=cons_total * 1.02,
                text=f"{cons_total:.0f}",
                showarrow=False,
                font=dict(size=10, color='darkred'),
                bgcolor="rgba(255,255,255,0.8)",
                bordercolor="darkred",
                borderwidth=1
            )
    
    try:
        logging.info(f"Successfully created interactive monthly before banking plot for {plant_name}")
        return fig
    except Exception as e:
        logging.error(f"Error finalizing interactive monthly before banking plot for {plant_name}: {str(e)}")
        return fig


##Monthly Banking Settlement
def create_monthly_banking_settlement_chart(df: pd.DataFrame, plant_name: str) -> tuple[plt.Figure, pd.DataFrame]:
    """
    Create a chart showing monthly banking settlement breakdown (line + pie), integrating consumption + unsettled logic.
    """
    # Rename columns for clarity
    df = df.rename(columns={
        'total_matched_settled_sum': 'settlement_without_banking',
        'total_intra_settlement': 'intra_settlement',
        'total_inter_settlement': 'inter_settlement',
        'total_consumption_sum': 'consumption'
    })

    # Ensure datetime format and sorting
    df['month'] = pd.to_datetime(df['month'] + '-01')
    df = df.sort_values('month')

    # Derived metrics
    df['settlement_with_banking'] = df['intra_settlement'] + df['inter_settlement']
    df['total_settlement'] = df['settlement_with_banking'] + df['settlement_without_banking']
    df['replacement_percentage'] = np.where(
        df['consumption'] > 0,
        (df['total_settlement'] / df['consumption']) * 100,
        0
    )
    df['month_str'] = df['month'].dt.strftime('%b %Y')
    x = np.arange(len(df))

    # 🎨 Plotting
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 7))

    # --- Line Chart ---
    ax1.plot(x, df['total_settlement'], label='Total Settlement', marker='o', linewidth=2.5, color="blue")
    ax1.plot(x, df['settlement_with_banking'], label='With Banking', marker='s', linestyle='--', linewidth=2, color="orange")
    ax1.plot(x, df['settlement_without_banking'], label='Without Banking', marker='^', linestyle=':', linewidth=2, color="green")

    # # Annotate Replacement %
    for i, pct in enumerate(df['replacement_percentage']):
        ax1.text(
            x[i],
            df['total_settlement'].iloc[i] * 1.03,
            f"{pct:.1f}%",
            ha='center',
            va='bottom',
            fontsize=9,
            color='gray'
        )

    ax1.set_title(f"Monthly Banking Settlement\n{plant_name}", fontsize=14)
    ax1.set_xlabel("Month", fontsize=12)
    ax1.set_ylabel("Energy (kWh)", fontsize=12)
    ax1.set_xticks(x)
    ax1.set_xticklabels(df['month_str'], rotation=45, ha='right')
    ax1.yaxis.set_major_formatter(FuncFormatter(format_thousands))
    ax1.grid(True, axis='y', linestyle='--', alpha=0.6)
    ax1.legend(loc='upper left', frameon=False)

    # --- Pie Chart ---
    total_with_banking = df['settlement_with_banking'].sum()
    total_without_banking = df['settlement_without_banking'].sum()
    total_consumption = df['consumption'].sum()

    unsettled = total_consumption - (total_with_banking + total_without_banking)

    pie_values = [total_with_banking, total_without_banking, unsettled]
    pie_labels = ['With Banking', 'Without Banking', 'Unsettled']
    pie_colors = ['orange', 'green', 'gray']

    ax2.pie(
        pie_values,
        labels=pie_labels,
        colors=pie_colors,
        autopct='%1.1f%%',
        startangle=90,
        textprops={'fontsize': 11}
    )
    ax2.set_title("Total Banking Breakdown", fontsize=14)

    plt.tight_layout()
    return fig, df


def create_monthly_banking_settlement_chart_interactive(df: pd.DataFrame, plant_name: str):
    """
    Interactive Plotly version of monthly banking settlement breakdown with comprehensive hover functionality.
    """
    if df.empty:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(
            title=f"{plant_name} - Monthly Banking Settlement",
            xaxis=dict(showgrid=False, showticklabels=False),
            yaxis=dict(showgrid=False, showticklabels=False)
        )
        return fig

    # Rename columns for clarity
    df = df.rename(columns={
        'total_matched_settled_sum': 'settlement_without_banking',
        'total_intra_settlement': 'intra_settlement',
        'total_inter_settlement': 'inter_settlement',
        'total_consumption_sum': 'consumption'
    })

    # Ensure datetime format and sorting
    df['month'] = pd.to_datetime(df['month'] + '-01')
    df = df.sort_values('month')

    # Derived metrics
    df['settlement_with_banking'] = df['intra_settlement'] + df['inter_settlement']
    df['total_settlement'] = df['settlement_with_banking'] + df['settlement_without_banking']
    df['replacement_percentage'] = np.where(
        df['consumption'] > 0,
        (df['total_settlement'] / df['consumption']) * 100,
        0
    )
    df['month_str'] = df['month'].dt.strftime('%b %Y')
    
    # Calculate unsettled demand for each month
    df['unsettled'] = df['consumption'] - df['total_settlement']
    df['unsettled_percentage'] = np.where(
        df['consumption'] > 0,
        (df['unsettled'] / df['consumption']) * 100,
        0
    )
    
    # Create subplot figure with line chart and pie chart
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=[f"Monthly Banking Settlement - {plant_name}", "Total Banking Breakdown"],
        specs=[[{"secondary_y": False}, {"type": "pie"}]],
        column_widths=[0.7, 0.3],
        horizontal_spacing=0.1
    )
    
    # Prepare custom hover data for line chart
    customdata_line = []
    for i, row in df.iterrows():
        month = row['month_str']
        consumption = row['consumption']
        consumption_mwh = consumption / 1000
        total_settlement = row['total_settlement']
        total_settlement_mwh = total_settlement / 1000
        settlement_with_banking = row['settlement_with_banking']
        settlement_with_banking_mwh = settlement_with_banking / 1000
        settlement_without_banking = row['settlement_without_banking']
        settlement_without_banking_mwh = settlement_without_banking / 1000
        intra_settlement = row['intra_settlement']
        intra_settlement_mwh = intra_settlement / 1000
        inter_settlement = row['inter_settlement'] 
        inter_settlement_mwh = inter_settlement / 1000
        replacement_percentage = row['replacement_percentage']
        unsettled = row['unsettled']
        unsettled_mwh = unsettled / 1000
        unsettled_percentage = row['unsettled_percentage']
        
        customdata_line.append([
            month,
            consumption,
            consumption_mwh,
            total_settlement,
            total_settlement_mwh,
            settlement_with_banking,
            settlement_with_banking_mwh,
            settlement_without_banking,
            settlement_without_banking_mwh,
            intra_settlement,
            intra_settlement_mwh,
            inter_settlement,
            inter_settlement_mwh,
            replacement_percentage,
            unsettled,
            unsettled_mwh,
            unsettled_percentage
        ])

    # Add Total Settlement line
    fig.add_trace(
        go.Scatter(
            x=df['month_str'],
            y=df['total_settlement'],
            mode='lines+markers',
            name='Total Settlement',
            line=dict(color="blue", width=3),
            marker=dict(size=8, symbol='circle'),
            customdata=customdata_line,
            hovertemplate='<b>📅 Month:</b> %{customdata[0]}<br>' +
                         '<b>⚡ Total Settlement:</b> %{customdata[3]:,.0f} kWh (%{customdata[4]:.1f} MWh)<br>' +
                         '<b>🔌 Total Consumption:</b> %{customdata[1]:,.0f} kWh (%{customdata[2]:.1f} MWh)<br>' +
                        #  '<b>🎯 Replacement Rate:</b> %{customdata[13]:.1f}%<br>' +
                         '<b>❌ Unsettled Demand:</b> %{customdata[14]:,.0f} kWh (%{customdata[15]:.1f} MWh)<br>' +
                         '<b>📊 Unsettled %:</b> %{customdata[16]:.1f}%<br>' +
                         '<extra></extra>'
        ),
        row=1, col=1
    )

    # Add Settlement With Banking line
    fig.add_trace(
        go.Scatter(
            x=df['month_str'],
            y=df['settlement_with_banking'],
            mode='lines+markers',
            name='With Banking',
            line=dict(color="orange", width=2, dash='dash'),
            marker=dict(size=8, symbol='square'),
            customdata=customdata_line,
            hovertemplate='<b>📅 Month:</b> %{customdata[0]}<br>' +
                         '<b>🏦 With Banking Settlement:</b> %{customdata[5]:,.0f} kWh (%{customdata[6]:.1f} MWh)<br>' +
                         '<b>🔄 Intra Settlement:</b> %{customdata[9]:,.0f} kWh (%{customdata[10]:.1f} MWh)<br>' +
                         '<b>🔗 Inter Settlement:</b> %{customdata[11]:,.0f} kWh (%{customdata[12]:.1f} MWh)<br>' +
                         '<b>🔌 Total Consumption:</b> %{customdata[1]:,.0f} kWh (%{customdata[2]:.1f} MWh)<br>' +
                         
                         '<extra></extra>'
        ),
        row=1, col=1
    )

    # Add Settlement Without Banking line
    fig.add_trace(
        go.Scatter(
            x=df['month_str'],
            y=df['settlement_without_banking'],
            mode='lines+markers',
            name='Without Banking',
            line=dict(color="green", width=2, dash='dot'),
            marker=dict(size=8, symbol='triangle-up'),
            customdata=customdata_line,
            hovertemplate='<b>📅 Month:</b> %{customdata[0]}<br>' +
                         '<b>🏢 Without Banking Settlement:</b> %{customdata[7]:,.0f} kWh (%{customdata[8]:.1f} MWh)<br>' +
                         '<b>🔌 Total Consumption:</b> %{customdata[1]:,.0f} kWh (%{customdata[2]:.1f} MWh)<br>' +
                         '<extra></extra>'
        ),
        row=1, col=1
    )

    # Prepare data for pie chart
    total_with_banking = df['settlement_with_banking'].sum()
    total_without_banking = df['settlement_without_banking'].sum()
    total_consumption = df['consumption'].sum()
    total_unsettled = total_consumption - (total_with_banking + total_without_banking)

    pie_values = [total_with_banking, total_without_banking, total_unsettled]
    pie_labels = ['With Banking', 'Without Banking', 'Unsettled']
    pie_colors = ['orange', 'green', 'lightgray']
    
    # Custom hover data for pie chart (just MWh values)
    pie_customdata = [
        total_with_banking/1000,
        total_without_banking/1000,
        total_unsettled/1000
    ]

    # Add pie chart
    fig.add_trace(
        go.Pie(
            values=pie_values,
            labels=pie_labels,
            marker=dict(colors=pie_colors, line=dict(color='white', width=2)),
            textinfo='label+percent',
            textposition='auto',
            customdata=pie_customdata,
            hovertemplate='<b>📊 Category:</b> %{label}<br>' +
                         '<b>💡 Value:</b> %{value:,.0f} kWh (%{customdata:.1f} MWh)<br>' +
                         '<b>📈 Percentage:</b> %{percent}<br>' +
                         '<extra></extra>'
        ),
        row=1, col=2
    )

    # Update layout
    fig.update_layout(
        height=600,
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
       
    )

    # Update x-axis for line chart
    fig.update_xaxes(
        title_text="Month",
        tickangle=45,
        row=1, col=1
    )

    # Update y-axis for line chart
    fig.update_yaxes(
        title_text="Energy (kWh)",
        tickformat=',.0f',
        row=1, col=1
    )

    return fig






##Monthly ToD Settled Values Heatmap

def create_monthly_settled_heatmap(df: pd.DataFrame, plant_name: str):
    """
    Create a heatmap-style scatter plot showing Generation vs Consumption with ToD slots and settled values.
    X-axis: Generation, Y-axis: Consumption, Points grouped by ToD slots, Settled values displayed inside cells.
    """
    try:
        logging.info(f"Creating monthly settled heatmap for {plant_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for settled heatmap - {plant_name}")
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, "No data available for the selected period", ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Monthly ToD Settled Values Heatmap - {plant_name}", fontsize=16, fontweight='bold')
            return fig
        
        # Validate required columns
        required_columns = ['date', 'slot', 'generation_kwh', 'consumption_kwh', 'settled']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns for settled heatmap: {missing_columns}")
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, f"Missing columns: {', '.join(missing_columns)}", ha='center', va='center', fontsize=14, color='red')
            ax.set_title(f"Monthly ToD Settled Values Heatmap - {plant_name}", fontsize=16, fontweight='bold')
            return fig
            
    except Exception as e:
        logging.error(f"Error initializing settled heatmap for {plant_name}: {str(e)}")
        fig, ax = plt.subplots(figsize=(12, 8))
        ax.text(0.5, 0.5, "Error loading chart - please try again", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly ToD Settled Values Heatmap - {plant_name}", fontsize=16, fontweight='bold')
        return fig

    try:
        # Step 1: Extract month and normalize slots
        df['date'] = pd.to_datetime(df['date'])
        df['month'] = df['date'].dt.to_period('M').astype(str)
        df['slot'] = df['slot'].apply(normalize_slot_name)
        
        # Step 2: Aggregate data by month and slot
        agg_df = df.groupby(['month', 'slot']).agg({
            'generation_kwh': 'sum',
            'consumption_kwh': 'sum', 
            'settled': 'sum'
        }).reset_index()
        
        # Step 3: Get slot configuration
        slot_order = get_slot_order()
        slot_colors = get_slot_color_map()
        
        # Step 4: Create the plot
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Plot each slot with different colors
        for slot in slot_order:
            slot_data = agg_df[agg_df['slot'] == slot]
            
            if not slot_data.empty:
                # Scatter plot points
                scatter = ax.scatter(
                    slot_data['generation_kwh'],
                    slot_data['consumption_kwh'],
                    c=slot_colors.get(slot, '#aaa'),
                    s=200,  # Size of points
                    alpha=0.7,
                    edgecolors='black',
                    linewidth=1,
                    label=slot
                )
                
                # Add settled values as text inside each point
                for _, row in slot_data.iterrows():
                    ax.annotate(
                        f"{row['settled']:.0f}",
                        (row['generation_kwh'], row['consumption_kwh']),
                        ha='center',
                        va='center',
                        fontsize=9,
                        fontweight='bold',
                        color='white'
                    )
        
        # Step 5: Formatting
        ax.set_xlabel("Generation (kWh)", fontsize=12)
        ax.set_ylabel("Consumption (kWh)", fontsize=12)
        ax.set_title(f"Monthly ToD Settled Values Heatmap - {plant_name}", fontsize=16, fontweight='bold')
        
        # Format axes with thousands separator
        ax.xaxis.set_major_formatter(FuncFormatter(format_thousands))
        ax.yaxis.set_major_formatter(FuncFormatter(format_thousands))
        
        # Add grid
        ax.grid(True, alpha=0.3)
        
        # Add legend
        ax.legend(
            title="ToD Slots",
            loc='upper left',
            bbox_to_anchor=(1.01, 1),
            frameon=True
        )
        
        plt.tight_layout()
        logging.info(f"Successfully created settled heatmap for {plant_name}")
        return fig
        
    except Exception as e:
        logging.error(f"Error creating settled heatmap for {plant_name}: {str(e)}")
        fig, ax = plt.subplots(figsize=(12, 8))
        ax.text(0.5, 0.5, "Error processing data - please try again", ha='center', va='center', fontsize=14, color='red')
        ax.set_title(f"Monthly ToD Settled Values Heatmap - {plant_name}", fontsize=16, fontweight='bold')
        return fig


def create_monthly_settled_heatmap_interactive(df: pd.DataFrame, plant_name: str):
    """
    Interactive Plotly version of monthly ToD settled values heatmap.
    X-axis: Generation, Y-axis: Consumption, Points grouped by ToD slots, Settled values displayed with hover.
    """
    try:
        logging.info(f"Creating interactive monthly settled heatmap for {plant_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for interactive settled heatmap - {plant_name}")
            fig = go.Figure()
            fig.add_annotation(
                text="No data available for the selected period",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Monthly ToD Settled Values Heatmap - {plant_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
        
        # Validate required columns
        required_columns = ['date', 'slot', 'generation_kwh', 'consumption_kwh', 'settled']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns for interactive settled heatmap: {missing_columns}")
            fig = go.Figure()
            fig.add_annotation(
                text=f"Missing columns: {', '.join(missing_columns)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Monthly ToD Settled Values Heatmap - {plant_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
            
    except Exception as e:
        logging.error(f"Error initializing interactive settled heatmap for {plant_name}: {str(e)}")
        fig = go.Figure()
        fig.add_annotation(
            text="Error loading chart - please try again",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(
            title=f"Monthly ToD Settled Values Heatmap - {plant_name}",
            xaxis=dict(showgrid=False, showticklabels=False),
            yaxis=dict(showgrid=False, showticklabels=False)
        )
        return fig

    try:
        # Step 1: Process data
        df['date'] = pd.to_datetime(df['date'])
        df['month'] = df['date'].dt.to_period('M').astype(str)
        df['slot'] = df['slot'].apply(normalize_slot_name)
        
        # Step 2: Aggregate data by month and slot
        agg_df = df.groupby(['month', 'slot']).agg({
            'generation_kwh': 'sum',
            'consumption_kwh': 'sum', 
            'settled': 'sum'
        }).reset_index()
        
        # Step 3: Get slot configuration
        slot_order = get_slot_order()
        slot_colors = get_slot_color_map()
        slot_time_map = {slot: SLOT_METADATA[slot]["time"] for slot in SLOT_METADATA}
        
        # Step 4: Create the figure
        fig = go.Figure()
        
        # Plot each slot with different colors
        for slot in slot_order:
            slot_data = agg_df[agg_df['slot'] == slot]
            
            if not slot_data.empty:
                # Prepare custom hover data
                customdata = []
                for _, row in slot_data.iterrows():
                    generation_mwh = row['generation_kwh'] / 1000
                    consumption_mwh = row['consumption_kwh'] / 1000
                    settled_mwh = row['settled'] / 1000
                    slot_time = slot_time_map.get(slot, "N/A")
                    
                    customdata.append([
                        row['month'],
                        slot,
                        row['generation_kwh'],
                        generation_mwh,
                        row['consumption_kwh'],
                        consumption_mwh,
                        row['settled'],
                        settled_mwh,
                        slot_time
                    ])
                
                # Add scatter trace
                fig.add_trace(
                    go.Scatter(
                        x=slot_data['generation_kwh'],
                        y=slot_data['consumption_kwh'],
                        mode='markers+text',
                        text=slot_data['settled'].apply(lambda x: f"{x:.0f}"),
                        textposition="middle center",
                        textfont=dict(color="white", size=10),
                        marker=dict(
                            color=slot_colors.get(slot, '#aaa'),
                            size=40,
                            line=dict(color='black', width=2),
                            opacity=0.8
                        ),
                        name=slot,
                        customdata=customdata,
                        hovertemplate='<b>📅 Month:</b> %{customdata[0]}<br>' +
                                     '<b>🕐 ToD Slot:</b> %{customdata[1]} (%{customdata[8]})<br>' +
                                     '<b>⚡ Generation:</b> %{customdata[2]:,.0f} kWh (%{customdata[3]:.1f} MWh)<br>' +
                                     '<b>🔌 Consumption:</b> %{customdata[4]:,.0f} kWh (%{customdata[5]:.1f} MWh)<br>' +
                                     '<b>💰 Settled:</b> %{customdata[6]:,.0f} kWh (%{customdata[7]:.1f} MWh)<br>' +
                                     '<extra></extra>'
                    )
                )
        
        # Step 5: Update layout
        fig.update_layout(
            title=f"Monthly ToD Settled Values Heatmap - {plant_name}",
            xaxis=dict(
                title="Generation (kWh)",
                tickformat=',.0f',
                showgrid=True,
                gridcolor='rgba(0,0,0,0.1)'
            ),
            yaxis=dict(
                title="Consumption (kWh)",
                tickformat=',.0f',
                showgrid=True,
                gridcolor='rgba(0,0,0,0.1)'
            ),
            legend=dict(
                title="ToD Slots",
                orientation="v",
                yanchor="top",
                y=1,
                xanchor="left",
                x=1.01
            ),
            hovermode='closest',
            width=1000,
            height=600
        )
        
        logging.info(f"Successfully created interactive settled heatmap for {plant_name}")
        return fig
        
    except Exception as e:
        logging.error(f"Error creating interactive settled heatmap for {plant_name}: {str(e)}")
        fig = go.Figure()
        fig.add_annotation(
            text="Error processing data - please try again",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(title=f"Monthly ToD Settled Values Heatmap - {plant_name}")
        return fig

###ToD Generation vs Consumption

def create_tod_binned_plot(
    df: pd.DataFrame,
    plant_name: str,
    start_date: str,
    end_date: str = None
):
    """
    Create a ToD-binned bar plot comparing generation vs. consumption.
    """

    if df.empty:
        raise ValueError("No data to plot")

    # ✅ Normalize slot names
    df['slot'] = df['slot'].apply(normalize_slot_name)

    # ✅ Melt the DataFrame for seaborn
    df_melted = df.melt(
        id_vars='slot',
        value_vars=['generation_kwh', 'consumption_kwh'],
        var_name='Type',
        value_name='kWh'
    )

    # ✅ Prepare order and labels
    slot_order = get_slot_order()
    slot_labels = add_slot_labels_with_time()

    # ✅ Setup seaborn style
    sns.set(style="whitegrid")
    fig, ax = plt.subplots(figsize=(10, 6))

    # ✅ Create barplot
    sns.barplot(
        data=df_melted,
        x='slot',
        y='kWh',
        hue='Type',
        ax=ax,
        order=slot_order,
        palette={
            'generation_kwh': '#4CAF50',    # Green
            'consumption_kwh': '#FFC107'   # Amber
        },
        edgecolor='black',
        linewidth=0.5
    )

    # ✅ Add labels to each bar
    for container in ax.containers:
        ax.bar_label(container, fmt='%.0f', label_type='edge', fontsize=8, padding=2)

    # ✅ Title and axis labels
        # ✅ Title and axis labels
    title_date = f"{start_date} to {end_date}" if end_date and end_date != start_date else start_date
    ax.set_title(f"ToD Generation vs Consumption\n{plant_name} ({title_date})", fontsize=14)
    ax.set_xlabel("ToD Slot")
    ax.set_ylabel("Energy (kWh)")

    # ✅ Rename legend labels
    new_labels = {
        'generation_kwh': 'Generation Kwh',
        'consumption_kwh': 'Consumption Kwh'
    }
    handles, labels = ax.get_legend_handles_labels()
    updated_labels = [new_labels.get(label, label) for label in labels]
    ax.legend(handles, updated_labels, title="Type")

    # ✅ Set fixed x-ticks to avoid warning
    ax.set_xticks(range(len(slot_order)))
    ax.set_xticklabels([slot_labels.get(slot, slot) for slot in slot_order], rotation=45)

    plt.tight_layout()
    return fig






##ToD Generation
def format_thousands_tod(x, pos):
    """Format numbers for ToD charts with K suffix for thousands"""
    try:
        if x >= 1000:
            return f'{x/1000:.0f}K'
        else:
            return f'{x:.0f}'
    except (TypeError, ValueError) as e:
        logging.warning(f"Error formatting ToD number {x}: {str(e)}")
        return str(x) if x is not None else '0'
    except Exception as e:
        logging.error(f"Unexpected error in format_thousands_tod: {str(e)}")
        return '0'




def create_tod_generation_plot(df: pd.DataFrame, plant_name: str, start_date: str, end_date: str = None):
    """
    Create a smart Seaborn-styled stacked bar chart of ToD slot-wise generation,
    with dynamic bar width, date tick intervals, total annotations, and ordered legend.
    """
    if df.empty:
        return

    sns.set(style="whitegrid")

    # Normalize input
    df['slot'] = df['slot'].apply(normalize_slot_name)
    df['date'] = pd.to_datetime(df['date'])
    df['generation_kwh'] = df['generation_kwh'].astype(float)

    # Load slot config
    slot_order = get_slot_order()
    slot_colors = get_slot_color_map()

    # Pivot to date × slot
    pivot_df = df.pivot_table(
        index='date',
        columns='slot',
        values='generation_kwh',
        aggfunc='sum',
        fill_value=0
    )
    for slot in slot_order:
        if slot not in pivot_df.columns:
            pivot_df[slot] = 0.0
    pivot_df = pivot_df[slot_order]

    # Plot config
    fig, ax = plt.subplots(figsize=(12, 6))
    bottom = np.zeros(len(pivot_df), dtype=float)
    dates = pivot_df.index

    total_days = (dates[-1] - dates[0]).days + 1
    bar_width = min(0.8, max(0.3, 20 / total_days))  # Dynamically scale width

    bar_handles = []

    # Plot stacked bars (bottom to top)
    for slot in reversed(slot_order):
        values = pivot_df[slot].values
        bar = ax.bar(
            dates,
            values,
            bottom=bottom,
            label=slot,
            color=slot_colors.get(slot, '#4CAF50'),
            edgecolor='white',
            width=bar_width
        )
        bar_handles.append((slot, bar[0]))
        bottom += values

    # Annotate inside bars
    for i, total in enumerate(bottom):
        ax.text(
            dates[i],
            total * 0.5,
            f"{total:.0f}",
            ha='center',
            va='center',
            fontsize=9,
            fontweight='bold',
            color='black',
            rotation=90
        )

    # Axis labels and title
    ax.set_ylabel("Generation (kWh)", fontsize=12)
    ax.set_xlabel("Date", fontsize=12)
    label = f"{start_date} to {end_date}" if end_date and start_date != end_date else start_date
    ax.set_title(f"Daily ToD-wise Generation\n{plant_name} ({label})", fontsize=14)

    # Smart date formatting
    ax.set_xticks(dates)
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%d-%b'))
    ax.xaxis.set_major_locator(mdates.AutoDateLocator())
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

    # Format Y axis
    ax.yaxis.set_major_formatter(FuncFormatter(format_thousands))

    # Ordered legend (bottom to top of stack = top to bottom of legend)
    legend_handles = [handle for name in slot_order for s, handle in reversed(bar_handles) if s == name]
    legend_labels = [name for name in slot_order]
    ax.legend(legend_handles, legend_labels, title="ToD Slot", loc='upper left', bbox_to_anchor=(1.01, 1), frameon=False)

    plt.tight_layout()
    return fig






##ToD Consumption

def create_tod_consumption_plot(df: pd.DataFrame, plant_name: str, start_date: str, end_date: str = None):
    """
    Create a smart stacked bar chart with Morning Peak on top and Night Off-Peak at the bottom,
    for ToD slot-wise consumption per day with dynamic bar width, x-axis handling, and legend ordering.
    """
    if df.empty:
        return

    sns.set(style="whitegrid")

    # Clean and normalize input
    df['slot'] = df['slot'].apply(normalize_slot_name)
    df['date'] = pd.to_datetime(df['date'])
    df['consumption_kwh'] = df['consumption_kwh'].astype(float)

    # Slot configuration
    slot_order = get_slot_order()
    slot_colors = get_slot_color_map()

    # Pivot table
    pivot_df = df.pivot_table(
        index='date',
        columns='slot',
        values='consumption_kwh',
        aggfunc='sum',
        fill_value=0
    )
    for slot in slot_order:
        if slot not in pivot_df.columns:
            pivot_df[slot] = 0.0
    pivot_df = pivot_df[slot_order]

    # Prepare for plotting
    fig, ax = plt.subplots(figsize=(12, 6))
    bottom = np.zeros(len(pivot_df), dtype=float)
    dates = pivot_df.index

    total_days = (dates[-1] - dates[0]).days + 1
    bar_width = min(0.8, max(0.3, 20 / total_days))  # Adjust bar width dynamically

    bar_handles = []

    # Reverse slot order for stacking: bottom up
    for slot in reversed(slot_order):
        values = pivot_df[slot].values
        bars = ax.bar(
            dates,
            values,
            bottom=bottom,
            label=slot,
            color=slot_colors.get(slot, '#4CAF50'),
            edgecolor='white',
            width=bar_width
        )
        bar_handles.append((slot, bars[0]))  # Capture for legend
        bottom += values

    # Add value labels inside bars
    for i, total in enumerate(bottom):
        ax.text(
            dates[i],
            total * 0.5,
            f"{total:.0f}",
            ha='center',
            va='center',
            fontsize=9,
            fontweight='bold',
            color='black',
            rotation=90
        )

    # Styling
    ax.set_ylabel("Consumption (kWh)", fontsize=12)
    ax.set_xlabel("Date", fontsize=12)
    label = f"{start_date} to {end_date}" if end_date and start_date != end_date else start_date
    ax.set_title(f"Daily ToD-wise Consumption\n{plant_name} ({label})", fontsize=14)

    # Smart x-axis ticks
    ax.set_xticks(dates)
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%d-%b'))
    ax.xaxis.set_major_locator(mdates.AutoDateLocator())
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

    # Format Y-axis
    ax.yaxis.set_major_formatter(FuncFormatter(format_thousands))

    # Ordered legend (from bottom to top stack)
    legend_handles = [handle for name in slot_order for s, handle in reversed(bar_handles) if s == name]
    legend_labels = [name for name in slot_order]
    ax.legend(legend_handles, legend_labels, title="ToD Slot", loc='upper left', bbox_to_anchor=(1.01, 1), frameon=False)

    plt.tight_layout()
    return fig


def create_tod_consumption_plot_interactive(df: pd.DataFrame, plant_name: str, start_date: str, end_date: str = None):
    """
    Create an interactive Plotly stacked bar chart of ToD slot-wise consumption,
    with enhanced hover information and dynamic formatting.
    """
    if df.empty:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(
            xaxis=dict(showgrid=False, showticklabels=False),
            yaxis=dict(showgrid=False, showticklabels=False)
        )
        return fig

    # Clean and normalize input
    df['slot'] = df['slot'].apply(normalize_slot_name)
    df['date'] = pd.to_datetime(df['date'])
    df['consumption_kwh'] = df['consumption_kwh'].astype(float)

    # Slot configuration
    slot_order = get_slot_order()
    slot_colors = get_slot_color_map()

    # Pivot table
    pivot_df = df.pivot_table(
        index='date',
        columns='slot',
        values='consumption_kwh',
        aggfunc='sum',
        fill_value=0
    )
    for slot in slot_order:
        if slot not in pivot_df.columns:
            pivot_df[slot] = 0.0
    pivot_df = pivot_df[slot_order]

    # Create figure
    fig = go.Figure()
    
    # Calculate daily totals for percentage calculations
    daily_totals = pivot_df.sum(axis=1)
    
    # Get slot time information
    slot_time_map = {slot: SLOT_METADATA[slot]["time"] for slot in SLOT_METADATA}
    
    # Create stacked bars (bottom to top)
    for slot in reversed(slot_order):
        values = pivot_df[slot].values
        dates = pivot_df.index
        
        # Custom hover data
        customdata = []
        for i, date in enumerate(dates):
            slot_value = values[i]
            daily_total = daily_totals.iloc[i]
            
            # Calculate additional metrics
            slot_percentage = (slot_value / daily_total * 100) if daily_total > 0 else 0
            slot_mwh = slot_value / 1000
            slot_time = slot_time_map.get(slot, "N/A")
            
            customdata.append([
                date.strftime('%Y-%m-%d'),
                slot,
                slot_value,
                daily_total,
                f"{slot_percentage:.1f}%",
                slot_mwh,
                slot_time
            ])
        
        fig.add_trace(
            go.Bar(
                x=dates,
                y=values,
                name=slot,
                marker=dict(
                    color=slot_colors.get(slot, '#4CAF50'),
                    line=dict(color='white', width=1)
                ),
                customdata=customdata,
                hovertemplate='<b>📅 Date:</b> %{customdata[0]}<br>' +
                             '<b>🕐 Slot:</b> %{customdata[1]} (%{customdata[6]})<br>' +
                             '<b>🔌 Consumption:</b> %{customdata[2]:,.0f} kWh (%{customdata[5]:.1f} MWh)<br>' +
                             '<b>📊 Slot Share:</b> %{customdata[4]} of daily total<br>' +
                             '<b>📈 Daily Total:</b> %{customdata[3]:,.0f} kWh<br>' +
                             '<extra></extra>',
                showlegend=True
            )
        )
    
    # Update layout
    label = f"{start_date} to {end_date}" if end_date and start_date != end_date else start_date
    
    fig.update_layout(
        title=dict(
            text=f"Daily ToD-wise Consumption<br>{plant_name} ({label})",
            x=0.5,
            xanchor='center',
            font=dict(size=16)
        ),
        xaxis=dict(
            title="Date",
            tickformat='%d-%b',
            tickangle=45,
            type='date'
        ),
        yaxis=dict(
            title="Consumption (kWh)",
            tickformat=',.0f'
        ),
        barmode='stack',
        height=600,
        showlegend=True,
        legend=dict(
            title="ToD Slot",
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.01
        ),
        hovermode='closest'
    )
    
    # Add grid
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    
    # Add annotations for daily totals if there are few days (to avoid clutter)
    if len(pivot_df) <= 15:  # Only show totals for 15 days or fewer
        for i, (date, daily_total) in enumerate(daily_totals.items()):
            if daily_total > 0:
                fig.add_annotation(
                    x=date,
                    y=daily_total,
                    text=f"{daily_total:,.0f}",
                    showarrow=False,
                    yshift=10,
                    font=dict(size=10, color='black'),
                    bgcolor='rgba(255,255,255,0.8)',
                    bordercolor='black',
                    borderwidth=1
                )
    
    return fig


def create_tod_generation_plot_interactive(df: pd.DataFrame, plant_name: str, start_date: str, end_date: str = None):
    """
    Create an interactive Plotly stacked bar chart of ToD slot-wise generation,
    with enhanced hover information and dynamic formatting.
    """
    if df.empty:
        # Create empty figure with message
        fig = go.Figure()
        fig.add_annotation(
            text="No data available",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(
            xaxis=dict(showgrid=False, showticklabels=False),
            yaxis=dict(showgrid=False, showticklabels=False)
        )
        return fig

    # Normalize input
    df['slot'] = df['slot'].apply(normalize_slot_name)
    df['date'] = pd.to_datetime(df['date'])
    df['generation_kwh'] = df['generation_kwh'].astype(float)

    # Load slot config
    slot_order = get_slot_order()
    slot_colors = get_slot_color_map()

    # Pivot to date × slot
    pivot_df = df.pivot_table(
        index='date',
        columns='slot',
        values='generation_kwh',
        aggfunc='sum',
        fill_value=0
    )
    for slot in slot_order:
        if slot not in pivot_df.columns:
            pivot_df[slot] = 0.0
    pivot_df = pivot_df[slot_order]

    # Create figure
    fig = go.Figure()
    
    # Calculate daily totals for percentage calculations
    daily_totals = pivot_df.sum(axis=1)
    
    # Get slot time information
    slot_time_map = {slot: SLOT_METADATA[slot]["time"] for slot in SLOT_METADATA}
    
    # Create stacked bars (bottom to top)
    for slot in reversed(slot_order):
        values = pivot_df[slot].values
        dates = pivot_df.index
        
        # Custom hover data
        customdata = []
        for i, date in enumerate(dates):
            slot_value = values[i]
            daily_total = daily_totals.iloc[i]
            
            # Calculate additional metrics
            slot_percentage = (slot_value / daily_total * 100) if daily_total > 0 else 0
            slot_mwh = slot_value / 1000
            slot_time = slot_time_map.get(slot, "N/A")
            
            customdata.append([
                date.strftime('%Y-%m-%d'),
                slot,
                slot_value,
                daily_total,
                f"{slot_percentage:.1f}%",
                slot_mwh,
                slot_time
            ])
        
        fig.add_trace(
            go.Bar(
                x=dates,
                y=values,
                name=slot,
                marker=dict(
                    color=slot_colors.get(slot, '#4CAF50'),
                    line=dict(color='white', width=1)
                ),
                customdata=customdata,
                hovertemplate='<b>📅 Date:</b> %{customdata[0]}<br>' +
                             '<b>🕐 Slot:</b> %{customdata[1]} (%{customdata[6]})<br>' +
                             '<b>⚡ Generation:</b> %{customdata[2]:,.0f} kWh (%{customdata[5]:.1f} MWh)<br>' +
                             '<b>📊 Slot Share:</b> %{customdata[4]} of daily total<br>' +
                             '<b>📈 Daily Total:</b> %{customdata[3]:,.0f} kWh<br>' +
                             '<extra></extra>',
                showlegend=True
            )
        )
    
    # Update layout
    label = f"{start_date} to {end_date}" if end_date and start_date != end_date else start_date
    
    fig.update_layout(
        title=dict(
            text=f"Daily ToD-wise Generation<br>{plant_name} ({label})",
            x=0.5,
            xanchor='center',
            font=dict(size=16)
        ),
        xaxis=dict(
            title="Date",
            tickformat='%d-%b',
            tickangle=45,
            type='date'
        ),
        yaxis=dict(
            title="Generation (kWh)",
            tickformat=',.0f'
        ),
        barmode='stack',
        height=600,
        showlegend=True,
        legend=dict(
            title="ToD Slot",
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.01
        ),
        hovermode='closest'
    )
    
    # Add grid
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    
    # Add annotations for daily totals if there are few days (to avoid clutter)
    if len(pivot_df) <= 15:  # Only show totals for 15 days or fewer
        for i, (date, daily_total) in enumerate(daily_totals.items()):
            if daily_total > 0:
                fig.add_annotation(
                    x=date,
                    y=daily_total,
                    text=f"{daily_total:,.0f}",
                    showarrow=False,
                    yshift=10,
                    font=dict(size=10, color='black'),
                    bgcolor='rgba(255,255,255,0.8)',
                    bordercolor='black',
                    borderwidth=1
                )
    
    return fig


def create_mean_trend_vs_irregularities_plot(df: pd.DataFrame, client_name: str) -> go.Figure:
    """
    Create a Plotly plot showing mean trend vs irregularities for hourly generation data.
    
    Args:
        df (pd.DataFrame): Hourly generation data with columns: date, datetime, hour, generation_kwh, time
        client_name (str): Client name for the plot title
        
    Returns:
        go.Figure: Plotly figure object
    """
    try:
        logging.info(f"Creating mean trend vs irregularities plot for {client_name}")
        
        if df is None or df.empty:
            logging.warning(f"No data available for mean trend vs irregularities plot - {client_name}")
            fig = go.Figure()
            fig.add_annotation(
                text="No hourly generation data available for the selected period",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(
                title=f"Mean Trend vs Irregularities - {client_name}",
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False)
            )
            return fig
        
        # Validate required columns
        required_columns = ['time', 'generation_kwh', 'datetime']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns for mean trend plot: {missing_columns}")
            fig = go.Figure()
            fig.add_annotation(
                text="Data structure error - missing required columns",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                font=dict(size=20, color="red")
            )
            fig.update_layout(title=f"Mean Trend vs Irregularities - {client_name}")
            return fig
        
        # Calculate mean trend for each time interval (hour)
        trend = df.groupby('time')['generation_kwh'].mean().reset_index()
        trend.columns = ['Time', 'Mean_Generation']
        
        # Calculate standard deviation for each time interval
        std_dev = df.groupby('time')['generation_kwh'].std().reset_index()
        std_dev.columns = ['Time', 'Std_Generation']
        
        # Merge mean and std dev
        trend_stats = pd.merge(trend, std_dev, on='Time', how='inner')
        
        # Calculate thresholds for irregularities (2 standard deviations)
        trend_stats['Upper_Threshold'] = trend_stats['Mean_Generation'] + 2 * trend_stats['Std_Generation']
        trend_stats['Lower_Threshold'] = trend_stats['Mean_Generation'] - 2 * trend_stats['Std_Generation']
        
        # Identify irregularities
        df_with_stats = pd.merge(df, trend_stats, left_on='time', right_on='Time', how='left')
        
        # Flag irregularities (values outside 2 standard deviations)
        df_with_stats['is_irregular'] = (
            (df_with_stats['generation_kwh'] > df_with_stats['Upper_Threshold']) |
            (df_with_stats['generation_kwh'] < df_with_stats['Lower_Threshold'])
        )
        
        flagged_irregularities = df_with_stats[df_with_stats['is_irregular']].copy()
        
        # Create datetime string for hover
        flagged_irregularities['datetime_str'] = flagged_irregularities['datetime'].dt.strftime('%Y-%m-%d %H:%M')
        
        # Create the figure
        fig = go.Figure()
        
        # Plot mean trend as a line
        fig.add_trace(go.Scatter(
            x=trend['Time'],
            y=trend['Mean_Generation'],
            mode='lines+markers',
            name='Mean Trend',
            line=dict(color='blue', width=2),
            marker=dict(size=6),
            hovertemplate='<b>Time:</b> %{x}<br>' +
                         '<b>Mean Generation:</b> %{y:,.0f} kWh<br>' +
                         '<extra></extra>'
        ))
        
        # Plot irregularities as red points with hover info
        if not flagged_irregularities.empty:
            fig.add_trace(go.Scatter(
                x=flagged_irregularities['time'],
                y=flagged_irregularities['generation_kwh'],
                mode='markers',
                name='Irregularities',
                marker=dict(color='red', size=10, symbol='x'),
                text=flagged_irregularities['datetime_str'],
                hovertemplate='<b>DateTime:</b> %{text}<br>' +
                             '<b>Generation:</b> %{y:,.0f} kWh<br>' +
                             '<b>Time:</b> %{x}<br>' +
                             '<b>Status:</b> Irregular<br>' +
                             '<extra></extra>'
            ))
        
        # Add upper and lower threshold lines
        fig.add_trace(go.Scatter(
            x=trend_stats['Time'],
            y=trend_stats['Upper_Threshold'],
            mode='lines',
            name='Upper Threshold (+2σ)',
            line=dict(color='orange', width=1, dash='dash'),
            opacity=0.7,
            hovertemplate='<b>Time:</b> %{x}<br>' +
                         '<b>Upper Threshold:</b> %{y:,.0f} kWh<br>' +
                         '<extra></extra>'
        ))
        
        fig.add_trace(go.Scatter(
            x=trend_stats['Time'],
            y=trend_stats['Lower_Threshold'],
            mode='lines',
            name='Lower Threshold (-2σ)',
            line=dict(color='orange', width=1, dash='dash'),
            opacity=0.7,
            hovertemplate='<b>Time:</b> %{x}<br>' +
                         '<b>Lower Threshold:</b> %{y:,.0f} kWh<br>' +
                         '<extra></extra>'
        ))
        
        # Update layout
        fig.update_layout(
            title=f"Mean Trend vs Irregularities by Time of Day - {client_name}",
            xaxis_title="Time of Day",
            yaxis_title="Generation (kWh)",
            legend_title="Legend",
            hovermode="closest",
            height=600,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        # Format x-axis to show time properly
        fig.update_xaxes(
            tickangle=45,
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(128,128,128,0.2)'
        )
        
        # Format y-axis
        fig.update_yaxes(
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(128,128,128,0.2)',
            tickformat='.0f'
        )
        
        logging.info(f"Successfully created mean trend vs irregularities plot for {client_name}")
        logging.info(f"Plot includes {len(trend)} time intervals and {len(flagged_irregularities)} irregularities")
        
        return fig
        
    except Exception as e:
        logging.error(f"Error creating mean trend vs irregularities plot for {client_name}: {str(e)}")
        fig = go.Figure()
        fig.add_annotation(
            text="Error creating plot - please try again",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            font=dict(size=20, color="red")
        )
        fig.update_layout(title=f"Mean Trend vs Irregularities - {client_name}")
        return fig








